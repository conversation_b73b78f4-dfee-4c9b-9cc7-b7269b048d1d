<template>
  <view class="adoption-card" @click="$emit('click')">
    <!-- 卡片标题 -->
    <text class="card-title">我认养的果树</text>

    <!-- 卡片主要内容 -->
    <view class="card-main">
      <view class="stats-container">
        <view class="stat-item">
          <text class="stat-number">{{ adoptionStats.treeCount }}</text>
          <text class="stat-unit">棵果树</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ adoptionStats.equityCount }}</text>
          <text class="stat-unit">个权益</text>
        </view>
      </view>
      <view class="tree-icon">
        <image src="/static/tree-badge.png" class="tree-image" mode="aspectFit" />
      </view>
    </view>
  </view>
</template>

<script setup>
defineProps({
  adoptionStats: {
    type: Object,
    default: () => ({
      treeCount: 0,
      equityCount: 0
    })
  }
})

defineEmits(['click'])
</script>

<style lang="scss" scoped>
// 颜色变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #666666;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.adoption-card {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 20rpx 32rpx;
  margin-bottom: 32rpx;
  cursor: pointer;
  position: relative;

  .card-title {
    font-size: 32rpx;
    font-weight: 500;
    color: $text-dark;
    display: block;
    margin-bottom: 24rpx;
  }

  .card-main {
    @include flex-between;
    align-items: center;

    .stats-container {
      display: flex;
      gap: 130rpx;

      .stat-item {
        @include flex-center;
        flex-direction: column;
        gap: 8rpx;

        .stat-number {
          font-size: 48rpx;
          font-weight: bold;
          color: $primary-color;
          line-height: 1;
        }

        .stat-unit {
          font-size: 24rpx;
          color: $text-gray;
          line-height: 1;
        }
      }
    }

    .tree-icon {
      width: 178rpx;
      height: 232rpx;
      position: absolute;
      @include flex-center;
      top: -60rpx;
      right: 60rpx;

      .tree-image {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>